# Uncomment this line to define a global platform for your project
platform :ios, '16.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'PERMISSION_CAMERA=1',
        'PERMISSION_MICROPHONE=1',
        'PERMISSION_PHOTOS=1',
      ]

      # Comprehensive fix for BoringSSL-GRPC unsupported -G flag
      if target.name == 'BoringSSL-GRPC'
        # Clear all C flags and set safe ones
        config.build_settings['OTHER_CFLAGS'] = '-DOPENSSL_NO_ASM -w'
        config.build_settings['OTHER_CPLUSPLUSFLAGS'] = '-DOPENSSL_NO_ASM -w'
        config.build_settings['GCC_WARN_INHIBIT_ALL_WARNINGS'] = 'YES'
        config.build_settings['WARNING_CFLAGS'] = ''
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] = [
          '$(inherited)',
          'GRPC_ARES=0',
          'OPENSSL_NO_ASM=1'
        ]
        # Force specific compiler settings
        config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++14'
        config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
      end

      # Remove any -G flags from all targets
      ['OTHER_CFLAGS', 'OTHER_CPLUSPLUSFLAGS', 'WARNING_CFLAGS', 'GCC_CFLAGS_OTHER'].each do |setting|
        if config.build_settings[setting].is_a?(Array)
          config.build_settings[setting] = config.build_settings[setting].reject { |flag| flag.to_s.match?(/^-G/) }
        elsif config.build_settings[setting].is_a?(String)
          config.build_settings[setting] = config.build_settings[setting].gsub(/-G\S*\s*/, '').strip
        end
      end

      # Ensure proper architecture settings
      config.build_settings['ARCHS'] = 'arm64'
      config.build_settings['VALID_ARCHS'] = 'arm64'
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
    end
  end
end
