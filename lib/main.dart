
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:likewallet/controller/setting_controller/lazyPutController.dart';
import 'package:likewallet/controller/setting_controller/setting_controller.dart';
import 'package:likewallet/controller/translate/translate_controller.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/notificationService.dart';
import 'package:likewallet/view/login/index.dart';

// Background message handler must be a top-level function
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Ensure Firebase is initialized
  await Firebase.initializeApp();

  if (kDebugMode) {
    print('Handling a background message: ${message.messageId}');
    print('Background message data: ${message.data}');
    print('Background message notification: ${message.notification?.title}');
  }

  // You can perform background tasks here, but keep them lightweight
  // For example, you could store the notification in a local database

  // Note: You cannot directly interact with the UI from here
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Set the background message handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Request notification permissions for iOS
  if (Platform.isIOS) {
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );
  }

  // Load configuration
  try {
    await GlobalConfiguration()
        .loadFromUrl("https://new.likepoint.io/configAPInew");
  } catch (e) {
    print('Error loading configuration: $e');
  }

  // Initialize controllers
  await AppBindings.lazyLoadControllers();

  // Run the app
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {

    Get.put(SettingController(), permanent: true);

    return ScreenUtilInit(
      designSize: const Size(402, 874),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context , child) {
        return GetMaterialApp(
          builder: EasyLoading.init(),
          debugShowCheckedModeBanner: false,
          title: 'Likewallet',
          theme: ThemeData(
            fontFamily: 'Proxima Nova',
            scaffoldBackgroundColor: Colors.white70,
            primaryColor: const Color(0xFFFF8500),
            colorScheme:
            ColorScheme.fromSwatch().copyWith(secondary: Colors.amber),
          ),
          supportedLocales: const [
            Locale('en'),
            Locale('th'),
            Locale('lo'),
            Locale('km'),
          ],
          locale: const Locale('en'),
          fallbackLocale: const Locale('en'),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          translations: TranslationsService(),
          home: const Stack(
            children: <Widget>[
              HomePage(),
              Notify(),
            ],
          ),
        );
      },
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusNode().unfocus();
      },
      child: const IndexLike(),
    );
  }
}

class Notify extends StatefulWidget {
  const Notify({Key? key}) : super(key: key);

  @override
  _NotifyState createState() => _NotifyState();
}

class _NotifyState extends State<Notify> {
  RxString _homeScreenText = "Waiting for token...".obs;
  RxDouble heightAnimate = 0.0.obs;

  // Subscribe to Apple Push Notification Service for iOS devices
  Future<void> getAPNToken(FirebaseMessaging fcm) async {
    if (Platform.isIOS) {
      String? apnsToken = await fcm.getAPNSToken();
      if (apnsToken != null) {
        await fcm.subscribeToTopic('K4QBZ5ZDCH');
      } else {
        // Retry after a short delay if token is not immediately available
        await Future<void>.delayed(
          const Duration(seconds: 3),
        );
        apnsToken = await fcm.getAPNSToken();
        if (apnsToken != null) {
          await fcm.subscribeToTopic('K4QBZ5ZDCH');
        }
      }
    } else {
      // For Android, subscribe directly
      await fcm.subscribeToTopic('K4QBZ5ZDCH');
    }
  }

  // Subscribe to the LIKE_POINT_2_0 topic for all devices
  Future<void> subscribeToTopicLikePoint(FirebaseMessaging fcm) async {
    await fcm.subscribeToTopic('LIKE_POINT_2_0');
  }

  // Save notification token to storage
  Future<void> setTokenNotify(String tokenNotify) async {
    await Storage.save(StorageKeys.tokenNotify, tokenNotify);
    print('Notification token saved: $tokenNotify');
  }

  // Get notification token from storage
  String? getTokenNotify() {
    return Storage.get<String>(StorageKeys.tokenNotify);
  }

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  // Initialize Firebase Messaging and notification settings
  Future<void> _initializeNotifications() async {
    final fcm = FirebaseMessaging.instance;

    // Request permission for iOS
    if (Platform.isIOS) {
      NotificationSettings settings = await fcm.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      print('User granted permission: ${settings.authorizationStatus}');
    }

    // Subscribe to topics
    await subscribeToTopicLikePoint(fcm);
    if (Platform.isIOS) {
      await getAPNToken(fcm);
    }

    // Configure foreground notification presentation
    await fcm.setForegroundNotificationPresentationOptions(
      sound: true,
      badge: true,
      alert: true,
    );

    // Get and save FCM token
    String? token = await fcm.getToken();
    if (token != null && token.isNotEmpty) {
      await setTokenNotify(token);
      setState(() {
        _homeScreenText.value = "Push Messaging token: $token";
        print(_homeScreenText.value);
      });
    }

    // Handle token refresh
    fcm.onTokenRefresh.listen((newToken) {
      setTokenNotify(newToken);
      print('FCM token refreshed: $newToken');
    });

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle when notification is tapped and app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundNotificationTap);
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage remoteMessage) async {
    print('Got a message whilst in the foreground!');
    print('Message data: ${remoteMessage.data}');

    if (remoteMessage.notification != null) {
      print('Message also contained a notification: ${remoteMessage.notification}');

      Get.snackbar(
        remoteMessage.notification!.title ?? 'Notification',
        remoteMessage.notification!.body ?? 'You have a new notification',
        snackPosition: SnackPosition.TOP,
        snackStyle: SnackStyle.FLOATING,
        duration: const Duration(seconds: 7),
        padding: const EdgeInsets.only(top: 10, left: 6, right: 6, bottom: 10),
        icon: Container(
          width: 50,
          height: 50,
          alignment: Alignment.topLeft,
          margin: const EdgeInsets.only(left: 10, right: 6),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5),
            child: Image.asset(
              "assets/icon/icon.png",
            ),
          ),
        ),
        mainButton: remoteMessage.data['type']?.toString() != '1'
          ? TextButton(
              onPressed: () async {
                Get.back();
                // Handle notification action based on type
                _handleNotificationAction(remoteMessage);
              },
              child: Text(
                'รายละเอียด',
                style: TextStyle(color: Colors.black, fontSize: 12),
              ),
            )
          : null,
      );
    }
  }

  // Handle when notification is tapped in background
  void _handleBackgroundNotificationTap(RemoteMessage remoteMessage) {
    print('Notification tapped in background: ${remoteMessage.data}');
    _handleNotificationAction(remoteMessage);
  }

  // Handle notification action based on type
  void _handleNotificationAction(RemoteMessage remoteMessage) {
    // Get notification type from data
    String notificationType = remoteMessage.data['type']?.toString() ?? '';

    // Handle different notification types
    switch (notificationType) {
      case '2': // Transaction notification
        // Navigate to transaction details or history
        // Get.to(() => TransactionHistoryPage());
        break;
      case '3': // Profile update notification
        // Navigate to profile page
        // Get.to(() => ProfilePage());
        break;
      // Add more cases as needed
      default:
        // Default action or no action
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Empty container as this widget doesn't need UI
    return Container();
  }
}
