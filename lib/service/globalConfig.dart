import 'package:global_configuration/global_configuration.dart';

class AppEnv {
  AppEnv._();

  // Helper method to safely get values from GlobalConfiguration
  static String _getConfigValue(String key, String defaultValue) {
    try {
      var value = GlobalConfiguration().getValue(key);
      return value != null ? value.toString() : defaultValue;
    } catch (e) {
      print('Error getting config value for $key: $e');
      return defaultValue;
    }
  }

  static String chainId = _getConfigValue("chainId", "88");
  static String apiFee = _getConfigValue("apiFee", "https://api.likepoint.io/fee");
  static String apiCheck = _getConfigValue("apiCheck", "https://api.likepoint.io/check");
  static String gasClaim = _getConfigValue("gasClaim", "0.0001");
  static String apiSumSub = "https://kyc.likewalletapp.com/user/";
  static String likeapi = "https://likeapi-dev-2cvcjlddla-el.a.run.app";

//  tomochain mainnet
  static String apiUrl = "https://${_getConfigValue("apiUrl", "api.likepoint.io")}";
  static String OldAPI = _getConfigValue("OldAPI", "https://api.likepoint.io");
  static String apiLikepointBCT =
      'https://jwt5vh9tqb.execute-api.ap-southeast-1.amazonaws.com';
  static String rpcUrl = _getConfigValue("rpcUrl", "https://rpc.tomochain.com");
  static String PortfolioUrl = _getConfigValue("PortfolioUrl", "https://api.likepoint.io/portfolio");
  static String wsUrl = _getConfigValue("wsUrl", "wss://ws.tomochain.com");
  static String contractLock = _getConfigValue("contractLock", "******************************************");
  static String contractLike = _getConfigValue("contractLike", "******************************************");
  //new
  static String contractLotteryLock =
  _getConfigValue("contractLotteryLock", "******************************************");
  static String contractNFT = _getConfigValue("contractNFT", "******************************************");
  static String lottery = _getConfigValue("lottery", "******************************************");
  static String abi_contractLotteryLock =
  _getConfigValue("abi_contractLotteryLock", "[]");
  static String abi_contractNFT =
  _getConfigValue("abi_contractNFT", "[]");
  static String abi_lottery = _getConfigValue("abi_lottery", "[]");

  static String contractAirdrop =
  _getConfigValue("contractAirdrop", "******************************************");
  static String contractSlotMachine =
  _getConfigValue("contractSlotMachine", "******************************************");
  static String contractMessage =
  _getConfigValue("contractMessage", "******************************************");
  static String contractLoan = _getConfigValue("contractLoan", "******************************************");

  static String abiContractLike =
  _getConfigValue("abiContractLike", "[]");
  static String abiContractLock =
  _getConfigValue("abiContractLock", "[]");
  static String abiContractAirdrop =
  _getConfigValue("abiContractAirdrop", "[]");
  static String abiContractSlot =
  _getConfigValue("abiContractSlot", "[]");
  static String abiContractLoan =
  _getConfigValue("abiContractLoan", "[]");
  static String abiContractMessage =
  _getConfigValue("abiContractMessage", "[]");
  static String APIKEY = _getConfigValue("APIKEY", "your-api-key");
  static String SECRETKEY = _getConfigValue("SECRETKEY", "your-secret-key");

}