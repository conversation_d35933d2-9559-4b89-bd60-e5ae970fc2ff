// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:rxdart/rxdart.dart';
// import 'dart:io' show Platform;
// import 'package:flutter/foundation.dart';
// import 'package:permission_handler/permission_handler.dart';
//
// final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
// FlutterLocalNotificationsPlugin();
//
// final BehaviorSubject<ReceivedNotification> didReceiveLocalNotificationSubject =
// BehaviorSubject<ReceivedNotification>();
//
// final BehaviorSubject<String> selectNotificationSubject =
// BehaviorSubject<String>();
//
// String? selectedNotificationPayload;
//
// class NotificationService {
//   static Future<void> initialize() async {
//     const AndroidInitializationSettings initializationSettingsAndroid =
//     AndroidInitializationSettings('app_icon');
//
//     final DarwinInitializationSettings initializationSettingsIOS =
//     DarwinInitializationSettings(
//       requestAlertPermission: false,
//       requestBadgePermission: false,
//       requestSoundPermission: false,
//       onDidReceiveLocalNotification: (
//           int id,
//           String? title,
//           String? body,
//           String? payload,
//           ) async {
//         didReceiveLocalNotificationSubject.add(
//           ReceivedNotification(
//             id: id,
//             title: title,
//             body: body,
//             payload: payload,
//           ),
//         );
//       },
//     );
//
//     final InitializationSettings initializationSettings = InitializationSettings(
//       android: initializationSettingsAndroid,
//       iOS: initializationSettingsIOS,
//     );
//
//     await flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//       onDidReceiveNotificationResponse: (NotificationResponse response) async {
//         if (response.payload != null) {
//           debugPrint('notification payload: ${response.payload}');
//           selectedNotificationPayload = response.payload;
//           selectNotificationSubject.add(response.payload!);
//         }
//       },
//     );
//
//     // ขอ permission
//     if (Platform.isIOS) {
//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<
//           IOSFlutterLocalNotificationsPlugin>()
//           ?.requestPermissions(alert: true, badge: true, sound: true);
//     } else if (Platform.isAndroid) {
//       await Permission.notification.request();
//     }
//   }
// }
//
// class ReceivedNotification {
//   final int id;
//   final String? title;
//   final String? body;
//   final String? payload;
//
//   ReceivedNotification({
//     required this.id,
//     this.title,
//     this.body,
//     this.payload,
//   });
// }
