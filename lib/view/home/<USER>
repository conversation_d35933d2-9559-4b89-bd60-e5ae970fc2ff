import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/otherController/homeController.dart';
import 'package:likewallet/controller/otherController/logoStoreController.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/controller/otherController/startLockToWinController.dart';
import 'package:likewallet/view/reward/reward.dart';
import 'package:likewallet/view/scanPage/scanPage.dart';
import 'package:likewallet/view/transferPoint/mainBankPage.dart';
import 'package:lock_to_win/lock_to_win.dart';


class HomeBodyScreen extends StatefulWidget {
  static final GlobalKey<_HomeBodyScreenState> globalKey = GlobalKey<_HomeBodyScreenState>();

  const HomeBodyScreen({super.key});

  @override
  State<HomeBodyScreen> createState() => _HomeBodyScreenState();
}


class _HomeBodyScreenState extends State<HomeBodyScreen> with SingleTickerProviderStateMixin {
  static late _HomeBodyScreenState instance;

  ProfileController profileCtrl = Get.find<ProfileController>();
  LogoStoreController logoCtrl = Get.find<LogoStoreController>();

  late final homeCtrl;
  @override
  void initState() {
    super.initState();

    homeCtrl = Get.isRegistered<HomeController>() ? Get.find<HomeController>() : Get.put(HomeController());
    homeCtrl.tabController = TabController(length: 3, vsync: this);
    instance = this;
  }

  void goToSpendLike() {
    print("goToSpendLike called");
    print("controller index before: ${homeCtrl.tabController.index}");
    homeCtrl.tabController.animateTo(1);
    print("controller index after: ${homeCtrl.tabController.index}");
  }


  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: () => print('click Background'),
        child: Stack(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  alignment: Alignment.topLeft,
                  // color: Colors.red.withOpacity(0.5),
                  child: RotatedBox(
                    quarterTurns: 1,
                    child: TabBar(
                      controller: homeCtrl.tabController,
                      isScrollable: true,
                      tabAlignment: TabAlignment.start,
                      dividerColor: Colors.transparent,
                      indicator: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: [Color(0xff505DFF), Color(0xff3948FD)],
                        ),
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(100),
                          topLeft: Radius.circular(100),
                        ),
                      ),
                      indicatorSize: TabBarIndicatorSize.label,
                      labelColor: const Color(0xff52FFFF),
                      unselectedLabelColor: const Color(0xff2693FF),
                      labelStyle: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        height: 1.2,
                      ),
                      unselectedLabelStyle: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 12.sp,
                        fontWeight: FontWeight.normal,
                        height: 1.2,
                      ),
                      tabs: [
                        _title(context, 'main_earn'.tr, 1),
                        _title(context, 'main_spend'.tr, 2),
                        _title(context, 'main_invest'.tr, 3),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: homeCtrl.tabController,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 0.h),
                        child: ListView(
                          children: [
                            _earnLikeList(
                              context,
                              'main_lock'.tr,
                              LikeWalletImage.icon_locklike,
                              'locklike',
                            ),
                            _border(context),
                            InkWell(
                              onTap: () {
                                print('reward');
                                Get.to(() => Rewards());
                              },
                              child: _earnLikeList(
                                context,
                                'main_hourly'.tr,
                                LikeWalletImage.icon_reward,
                                'reward',
                              ),
                            ),
                            _border(context),
                            GestureDetector(
                              onTap: () async {
                                print(profileCtrl.tierLevel);
                                // Start LockToWin
                                final lockToWin = StartLockToWin();
                                var data = await lockToWin.startLockToWin(
                                    context, profileCtrl.tierLevel);
                                print("GestureDetector");
                                print(data);
                                LockToWin.startLockToWin(
                                    data: {"data": data});
                              },
                              child: Container(
                                padding:
                                EdgeInsets.symmetric(horizontal: 30.w),
                                child: Row(
                                  children: [
                                    SizedBox(width: 28.w),
                                    Image.asset(
                                      LikeWalletImage.icon_lock_to_win,
                                      height: 57.h,
                                      width: 55.w,
                                    ),
                                    SizedBox(width: 28.w),
                                    Text(
                                      'ltw_title'.tr,
                                      style: TextStyle(
                                        fontFamily: 'Proxima Nova',
                                        fontSize: 12.sp,
                                        letterSpacing: 1,
                                        color: const Color(0xe5ffffff),
                                        height: 1.2,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            _border(context),
                          ],
                        ),
                      ),
                      _spendLike(context),
                      _investLike(context),
                    ],
                  ),
                ),
              ],
            ),
            _bank(context),
          ],
        ),
      ),
    );
  }

  Widget _title(BuildContext context, String text, int value) {
    return Container(
      height: 95.w,
      width: 62.h,
      alignment: Alignment.center,
      child: RotatedBox(
        quarterTurns: -1,
        child: Text(
          text,
          style: TextStyle(
            fontFamily: 'Proxima Nova',
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
            letterSpacing: 1,
            height: 1.2,
          ),
        ),
      ),
    );
  }

  Widget _earnLikeList(
      BuildContext context, String text, String icon, String docName) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Row(
        children: [
          SizedBox(width: 28.w),
          Image.asset(
            icon,
            height: 57.h,
            width: 55.w,
          ),
          SizedBox(width: 28.w),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 12.sp,
              letterSpacing: 1,
              color: const Color(0xe5ffffff),
              height: 1.2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _spendLike(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 45.h),
      child: Obx(
            () => Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    // logoCtrl.listSpendkpayShop();
                  },
                  child: Container(
                    width: 75.w,
                    height: 75.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      // color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.16),
                          offset: const Offset(0, 3),
                          blurRadius: 6.0,
                          spreadRadius: 1.0,
                        ),
                      ],
                    ),
                    child: Image.network(
                      logoCtrl.list[0].logo.toString(),
                      height: 100.h,
                    ),
                  ),
                ),
                SizedBox(width: 45.w),
                GestureDetector(
                  onTap: () => print('click Spend Shop 2'),
                  child: Container(
                    width: 75.w,
                    height: 75.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      // color: Colors.red,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.16),
                          offset: const Offset(0, 3),
                          blurRadius: 6.0,
                          spreadRadius: 1.0,
                        ),
                      ],
                    ),
                    child: Image.network(
                      logoCtrl.list[1].logo.toString(),
                      height: 100.h,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 35.h),
          ],
        ),
      ),
    );
  }

  Widget _investLike(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 45.h),
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: double.infinity,
            child: Text(
              'main_detail'.tr,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                color: const Color(0xffAFF8FF).withOpacity(0.9),
                fontSize: 18.sp,
              ),
            ),
          ),
          SizedBox(height: 15.h),
        ],
      ),
    );
  }

  Widget _border(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 25.h),
      child: SvgPicture.string(
        '<svg viewBox="352.0 846.0 653.0 4.0"><defs><linearGradient id="gradient" x1="0.920288" y1="0.0" x2="0.039928" y2="0.0"><stop offset="0.0" stop-color="#00ffffff" stop-opacity="0.0" /><stop offset="0.246305" stop-color="#b3ffffff" stop-opacity="0.7" /><stop offset="0.522168" stop-color="#ffffffff" /><stop offset="0.768473" stop-color="#b3ffffff" stop-opacity="0.7" /><stop offset="1.0" stop-color="#00ffffff" stop-opacity="0.0" /></linearGradient></defs><path transform="translate(352.0, 846.0)" d="M 0 0 L 653 0 L 653 4 L 0 4 L 0 0 Z" fill="url(#gradient)" fill-opacity="0.08" stroke="none" stroke-width="1" stroke-opacity="0.08" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
        allowDrawingOutsideViewBox: true,
      ),
    );
  }

  Widget _bank(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          left: 30.w,
          bottom: 180.h,
          child: GestureDetector(
            onTap: () {
              // profileCtrl.getName();
              print('click Banking');
              Get.to(() => MainBankPage());
            },
            child: Image.asset(
              LikeWalletImage.icon_banking,
              height: 35.h,
            ),
          ),
        ),
      ],
    );
  }

}
